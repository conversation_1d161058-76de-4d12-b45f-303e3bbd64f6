# app.py
import streamlit as st
import pandas as pd
import data_manager
import calculations
import reporting
import io # For download buttons

# --- Page Configuration ---
st.set_page_config(layout="wide", page_title="Lightweight ERP")

# --- Initialize Database/Load Initial Data (run once) ---
@st.cache_resource # Cache the database connection setup
def initialize_db():
    data_manager.create_tables()
    # Optionally, load initial data from existing CSVs into DB if not already there
    # For example:
    # if not data_manager.load_data_from_db('sales').empty:
    #    sales_df_initial = pd.read_excel('your_spreadsheet.xlsx', sheet_name='SALES')
    #    data_manager.save_df_to_db(sales_df_initial, 'sales', if_exists='replace')
initialize_db()

# --- Session State for Data ---
if 'sales_df' not in st.session_state:
    st.session_state.sales_df = data_manager.load_data_from_db('sales')
if 'inventory_df' not in st.session_state:
    st.session_state.inventory_df = data_manager.load_data_from_db('inventory')
# ... and so on for all your data categories

st.sidebar.title("ERP Navigation")
selected_tab = st.sidebar.radio("Go to", ["Dashboard", "Income & Expense", "Sales Tracker", "Inventory", "Product Recipes", "Purchases", "Customers", "Financial Goals"])

# --- Main Content Area ---
st.title("Lightweight ERP System")

if selected_tab == "Dashboard":
    st.header("Overall Performance Dashboard")
    # Load relevant data
    income_expense_df = data_manager.load_data_from_db('income_expenses')
    sales_df = data_manager.load_data_from_db('sales')

    # Perform calculations
    monthly_summary_df = reporting.create_monthly_summary_df(income_expense_df, sales_df)
    total_profit = monthly_summary_df['Net Profit'].sum()

    st.metric(label="Total Net Profit YTD", value=f"${total_profit:,.2f}")
    st.plotly_chart(reporting.plot_monthly_profit(monthly_summary_df), use_container_width=True)

    st.subheader("Monthly Summary")
    st.dataframe(monthly_summary_df)

    # Sales Tax Dashboard
    st.subheader("Sales Tax Overview")
    sales_with_tax = calculations.calculate_tax_collected(sales_df.copy())
    total_tax_collected = sales_with_tax['sales_tax_collected'].sum()
    st.metric(label="Total Sales Tax Collected YTD", value=f"${total_tax_collected:,.2f}")
    # Add a chart for tax collected vs. paid if you track paid tax
    st.dataframe(sales_with_tax[['sale_date', 'total_amount', 'tax_rate', 'sales_tax_collected']].head())


elif selected_tab == "Income & Expense":
    st.header("Income & Expense Log")
    # Display table with editing
    current_income_expenses = data_manager.load_data_from_db('income_expenses')
    edited_df = st.data_editor(current_income_expenses, num_rows="dynamic", use_container_width=True)

    if st.button("Save Income/Expense Changes"):
        data_manager.save_df_to_db(edited_df, 'income_expenses', if_exists='replace')
        st.success("Income/Expenses updated!")
        st.session_state.income_expenses_df = edited_df # Update session state

    # Add new income/expense form
    with st.form("add_income_expense_form"):
        st.subheader("Add New Entry")
        date = st.date_input("Date")
        category = st.text_input("Category")
        description = st.text_area("Description")
        amount = st.number_input("Amount", min_value=0.0, format="%.2f")
        entry_type = st.radio("Type", ["Income", "Expense"])
        submitted = st.form_submit_button("Add Entry")
        if submitted:
            data_manager.add_income_expense(str(date), category, description, amount, entry_type)
            st.success("Entry added!")
            st.session_state.income_expenses_df = data_manager.load_data_from_db('income_expenses') # Refresh

elif selected_tab == "Sales Tracker":
    st.header("Sales Log")
    current_sales = data_manager.load_data_from_db('sales')
    st.dataframe(current_sales)

    with st.form("add_sale_form"):
        st.subheader("Add New Sale")
        sale_date = st.date_input("Sale Date")
        # ... other fields for sale data ...
        submitted_sale = st.form_submit_button("Add Sale")
        if submitted_sale:
            # Call a function in data_manager to add sale
            st.success("Sale added!")
            # Refresh data in session state

    # Invoice Generation Example (simple)
    st.subheader("Generate Invoice")
    invoice_sale_id = st.number_input("Enter Sale ID for Invoice", min_value=1)
    if st.button("Generate PDF Invoice"):
        # Fetch sale details and customer info based on invoice_sale_id
        # Prepare invoice_data dictionary
        # For demonstration, let's create dummy invoice_data
        dummy_invoice_data = {
            'invoice_number': f"INV-{invoice_sale_id}",
            'invoice_date': '2025-06-30',
            'customer_name': 'Test Customer Inc.',
            'customer_address': '123 Main St, Anytown, USA',
            'items': [{'description': 'Product A', 'quantity': 2, 'unit_price': 50.00, 'total': 100.00}],
            'subtotal': 100.00,
            'tax': 8.00,
            'grand_total': 108.00
        }
        pdf_buffer = io.BytesIO()
        reporting.generate_invoice_pdf(dummy_invoice_data, pdf_buffer)
        pdf_buffer.seek(0) # Rewind the buffer to the beginning
        st.download_button(
            label="Download Invoice PDF",
            data=pdf_buffer,
            file_name=f"invoice_{invoice_sale_id}.pdf",
            mime="application/pdf"
        )

# ... Add sections for Inventory, Product Recipes, Purchases, Customers, Financial Goals ...