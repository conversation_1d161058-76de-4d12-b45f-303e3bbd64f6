# reporting.py
import pandas as pd
import plotly.express as px

def create_monthly_summary_df(income_expense_df, sales_df):
    # Aggregate income
    income_df = income_expense_df[income_expense_df['type'] == 'Income'].groupby(pd.to_datetime(income_expense_df['date']).dt.to_period('M'))['amount'].sum().rename('Income')
    # Aggregate expenses
    expense_df = income_expense_df[income_expense_df['type'] == 'Expense'].groupby(pd.to_datetime(income_expense_df['date']).dt.to_period('M'))['amount'].sum().rename('Expenses')
    # Aggregate COGS from sales (need COGS data in sales_df)
    # ...
    # Combine
    summary_df = pd.concat([income_df, expense_df], axis=1).fillna(0)
    summary_df['Net Profit'] = summary_df['Income'] - summary_df['Expenses']
    return summary_df.reset_index().rename(columns={'index': 'Month'})

def plot_monthly_profit(summary_df):
    fig = px.bar(summary_df, x='Month', y='Net Profit', title='Monthly Profit Trend')
    return fig
	
# reporting.py (continued for PDF)
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors

def generate_invoice_pdf(invoice_data, filename="invoice.pdf"):
    doc = SimpleDocTemplate(filename, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []

    # Add header info
    story.append(Paragraph("<b>Invoice</b>", styles['h1']))
    story.append(Spacer(1, 0.2 * 1.5 * 10)) # Adjust spacing

    # Customer and Invoice details (placeholder)
    story.append(Paragraph(f"<b>Invoice #:</b> {invoice_data['invoice_number']}", styles['Normal']))
    story.append(Paragraph(f"<b>Date:</b> {invoice_data['invoice_date']}", styles['Normal']))
    story.append(Paragraph(f"<b>Billed To:</b> {invoice_data['customer_name']}", styles['Normal']))
    story.append(Paragraph(f"{invoice_data['customer_address']}", styles['Normal']))
    story.append(Spacer(1, 0.2 * 10))

    # Items table
    data = [['Item', 'Quantity', 'Unit Price', 'Total']]
    for item in invoice_data['items']:
        data.append([item['description'], item['quantity'], f"${item['unit_price']:.2f}", f"${item['total']:.2f}"])
    data.append(['', '', 'Subtotal:', f"${invoice_data['subtotal']:.2f}"])
    data.append(['', '', 'Tax:', f"${invoice_data['tax']:.2f}"])
    data.append(['', '', 'Total:', f"${invoice_data['grand_total']:.2f}"])

    table_style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0,0), (-1,-1), 1, colors.black)
    ])
    table = Table(data)
    table.setStyle(table_style)
    story.append(table)
    story.append(Spacer(1, 0.5 * 10))
    story.append(Paragraph("Thank you for your business!", styles['Normal']))

    doc.build(story)