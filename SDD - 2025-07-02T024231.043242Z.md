# Software Design Document (SDD)
## Inventory Cost Management System

**Document Version:** 1.0
**Date:** July 2, 2025
**Prepared in accordance with IEEE Std 1016-2009**

---

## Table of Contents

1. [Introduction](#1-introduction)
2. [Design Overview](#2-design-overview)
3. [System Architecture](#3-system-architecture)
4. [Design Viewpoints](#4-design-viewpoints)
   - 4.1 [Context Viewpoint](#41-context-viewpoint)
   - 4.2 [Composition Viewpoint](#42-composition-viewpoint)
   - 4.3 [Logical Viewpoint](#43-logical-viewpoint)
   - 4.4 [Dependency Viewpoint](#44-dependency-viewpoint)
   - 4.5 [Information Viewpoint](#45-information-viewpoint)
   - 4.6 [Patterns Viewpoint](#46-patterns-viewpoint)
   - 4.7 [Resource Viewpoint](#47-resource-viewpoint)
   - 4.8 [Interface Viewpoint](#48-interface-viewpoint)
5. [Detailed Design](#5-detailed-design)
6. [Design Rationale](#6-design-rationale)
7. [Appendices](#7-appendices)

---

## 1. Introduction

### 1.1 Purpose

This Software Design Document (SDD) describes the architecture and detailed design of the Inventory Cost Management System, a lightweight ERP solution designed for small to medium businesses. The document serves as a comprehensive guide for developers, maintainers, and stakeholders involved in the system's implementation and evolution.

### 1.2 Scope

The Inventory Cost Management System is a web-based application that provides:
- Financial tracking and reporting
- Sales management and invoice generation
- Inventory management with real-time updates
- Product recipe and bill of materials (BOM) management
- Customer relationship management
- Financial goal setting and monitoring

### 1.3 Definitions and Acronyms

- **SDD**: Software Design Document
- **ERP**: Enterprise Resource Planning
- **BOM**: Bill of Materials
- **SKU**: Stock Keeping Unit
- **COGS**: Cost of Goods Sold
- **UI**: User Interface
- **API**: Application Programming Interface
- **CRUD**: Create, Read, Update, Delete

### 1.4 References

- IEEE Std 1016-2009: IEEE Standard for Information Technology—Systems Design—Software Design Descriptions
- Streamlit Documentation: https://docs.streamlit.io/
- SQLite Documentation: https://www.sqlite.org/docs.html
- Pandas Documentation: https://pandas.pydata.org/docs/
- ReportLab Documentation: https://www.reportlab.com/docs/

### 1.5 Overview

This document is organized according to IEEE 1016-2009 standards, presenting multiple design viewpoints to provide comprehensive coverage of the system's architecture, behavior, and implementation details.

---

## 2. Design Overview

### 2.1 Design Goals

The primary design goals for the Inventory Cost Management System are:

1. **Simplicity**: Provide an intuitive interface for non-technical users
2. **Modularity**: Ensure components are loosely coupled and highly cohesive
3. **Scalability**: Support growth in data volume and user base
4. **Reliability**: Maintain data integrity and system availability
5. **Maintainability**: Enable easy updates and feature additions
6. **Performance**: Provide responsive user experience
7. **Cost-effectiveness**: Minimize infrastructure and licensing costs

### 2.2 Design Constraints

- **Technology Stack**: Python-based solution using Streamlit for web interface
- **Database**: SQLite for simplicity and portability
- **Deployment**: Single-machine deployment initially
- **Browser Compatibility**: Modern web browsers (Chrome, Firefox, Safari, Edge)
- **Data Volume**: Designed for small to medium business scale (< 100,000 transactions/year)

### 2.3 Design Principles

- **Separation of Concerns**: Clear separation between data management, business logic, and presentation
- **DRY (Don't Repeat Yourself)**: Reusable components and functions
- **SOLID Principles**: Following object-oriented design principles
- **Data-Driven Design**: Leveraging pandas DataFrames for data manipulation
- **Progressive Enhancement**: Core functionality works without advanced features

---

## 3. System Architecture

### 3.1 Architectural Overview

The Inventory Cost Management System follows a layered architecture pattern with clear separation between presentation, business logic, and data access layers. The system is designed as a monolithic application with modular components for easy maintenance and future enhancement.

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Dashboard     │ │   Forms &       │ │   Reports &     ││
│  │   Components    │ │   Data Entry    │ │   Visualizations││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Business Logic Layer                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Calculations  │ │   Reporting     │ │   Data Manager  ││
│  │   Module        │ │   Module        │ │   Module        ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Data Access Layer                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   SQLite        │ │   Pandas        │ │   File System   ││
│  │   Database      │ │   DataFrames    │ │   (Reports)     ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Component Architecture

#### 3.2.1 Core Components

1. **Application Controller (app.py)**
   - Main entry point and navigation controller
   - Session state management
   - Page routing and component orchestration

2. **Data Manager (data_manager.py)**
   - Database connection and operations
   - CRUD operations for all entities
   - Data validation and integrity

3. **Calculations Module (calculations.py)**
   - Business logic for financial calculations
   - Inventory level updates
   - Profit and margin calculations

4. **Reporting Module (reporting.py)**
   - Report generation and formatting
   - Data visualization
   - PDF invoice generation

#### 3.2.2 Data Entities

The system manages the following primary data entities:

- **Sales**: Transaction records with customer, product, and financial details
- **Inventory**: Current stock levels and product information
- **Customers**: Customer contact and transaction history
- **Products**: Product catalog with recipes and BOMs
- **Income/Expenses**: Financial transaction records
- **Purchases**: Supplier transactions and material acquisitions

### 3.3 Technology Stack

#### 3.3.1 Frontend Technologies
- **Streamlit**: Web application framework for Python
- **Plotly**: Interactive data visualization
- **HTML/CSS**: Custom styling and layout

#### 3.3.2 Backend Technologies
- **Python 3.8+**: Core programming language
- **Pandas**: Data manipulation and analysis
- **SQLite**: Embedded database system
- **ReportLab**: PDF generation

#### 3.3.3 Development Tools
- **Git**: Version control
- **VS Code**: Development environment
- **pytest**: Unit testing framework

### 3.4 Deployment Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      Client Browser                         │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Dashboard     │ │   Forms         │ │   Reports       ││
│  │   Interface     │ │   Interface     │ │   Interface     ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/WebSocket
┌─────────────────────────────────────────────────────────────┐
│                    Streamlit Server                         │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Python Application                         ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐││
│  │  │    app.py   │ │calculations │ │    reporting.py     │││
│  │  │             │ │    .py      │ │                     │││
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘││
│  │  ┌─────────────────────────────────────────────────────┐││
│  │  │              data_manager.py                        │││
│  │  └─────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │ File I/O
┌─────────────────────────────────────────────────────────────┐
│                      Local File System                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   SQLite DB     │ │   Generated     │ │   Configuration ││
│  │   Files         │ │   Reports       │ │   Files         ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

---

## 4. Design Viewpoints

### 4.1 Context Viewpoint

#### 4.1.1 System Context

The Inventory Cost Management System operates within the context of a small to medium business environment, interfacing with various external entities and systems.

**External Entities:**
- **Business Users**: Managers, accountants, and staff who interact with the system
- **Customers**: External entities whose information is managed by the system
- **Suppliers**: External vendors from whom materials and products are purchased
- **Tax Authorities**: Regulatory bodies requiring financial reporting
- **Auditors**: External parties requiring access to financial records

**System Boundaries:**
- The system manages internal business operations and data
- External integrations are limited to file imports/exports
- No real-time external API integrations in the current version
- Data exchange occurs through standard formats (CSV, PDF)

#### 4.1.2 Operating Environment

- **Hardware Platform**: Standard business computers (Windows, macOS, Linux)
- **Software Platform**: Python 3.8+ runtime environment
- **Network Requirements**: Local network access for multi-user scenarios
- **Browser Requirements**: Modern web browsers with JavaScript enabled
- **Storage Requirements**: Local file system access for database and reports

### 4.2 Composition Viewpoint

#### 4.2.1 System Decomposition

The system is decomposed into four primary modules, each with specific responsibilities:

**1. Application Module (app.py)**
```
Application Module
├── Navigation Controller
├── Session State Manager
├── Page Routing Logic
├── User Interface Components
└── Event Handlers
```

**2. Data Management Module (data_manager.py)**
```
Data Management Module
├── Database Connection Manager
├── Table Creation and Schema Management
├── CRUD Operations
│   ├── Sales Operations
│   ├── Inventory Operations
│   ├── Customer Operations
│   └── Financial Operations
├── Data Validation
└── Transaction Management
```

**3. Business Logic Module (calculations.py)**
```
Business Logic Module
├── Financial Calculations
│   ├── Profit Calculation
│   ├── Margin Calculation
│   └── Tax Calculation
├── Inventory Management
│   ├── Stock Level Updates
│   ├── BOM Processing
│   └── Reorder Point Calculation
└── Data Transformation
```

**4. Reporting Module (reporting.py)**
```
Reporting Module
├── Data Aggregation
├── Visualization Generation
│   ├── Charts and Graphs
│   └── Dashboard Metrics
├── Report Formatting
└── PDF Generation
    ├── Invoice Generation
    └── Financial Reports
```

#### 4.2.2 Module Interactions

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    app.py   │───▶│data_manager │───▶│  Database   │
│             │    │    .py      │    │   (SQLite)  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │
       ▼                   ▼
┌─────────────┐    ┌─────────────┐
│calculations │    │ reporting   │
│    .py      │    │    .py      │
└─────────────┘    └─────────────┘
       │                   │
       └───────────────────┘
              │
              ▼
       ┌─────────────┐
       │   Output    │
       │ (Reports,   │
       │ Invoices)   │
       └─────────────┘
```

### 4.3 Logical Viewpoint

#### 4.3.1 Logical Architecture

The system follows a Model-View-Controller (MVC) pattern adapted for Streamlit's reactive programming model:

**Model Layer:**
- Data entities represented as pandas DataFrames
- Database schema managed through SQLite
- Business rules implemented in calculation functions

**View Layer:**
- Streamlit components for user interface
- Interactive forms and data displays
- Visualization components using Plotly

**Controller Layer:**
- Navigation logic in app.py
- Event handling through Streamlit callbacks
- State management using session state

#### 4.3.2 Control Flow

```
User Action → Streamlit Event → Controller Logic → Business Logic → Data Layer → Database
     ↑                                                                              │
     └─────────────── UI Update ← View Rendering ← Data Processing ←───────────────┘
```

#### 4.3.3 Data Flow

1. **Input Flow**: User inputs → Form validation → Data transformation → Database storage
2. **Processing Flow**: Database queries → Business calculations → Result aggregation
3. **Output Flow**: Processed data → Visualization/Reporting → User interface display

---

### 4.4 Dependency Viewpoint

#### 4.4.1 Module Dependencies

```
app.py
├── depends on: streamlit, pandas, data_manager, calculations, reporting, io
├── imports: data_manager, calculations, reporting
└── runtime dependencies: streamlit framework

data_manager.py
├── depends on: pandas, sqlite3
├── provides: database operations, CRUD functions
└── used by: app.py, calculations.py, reporting.py

calculations.py
├── depends on: pandas
├── provides: business logic functions
└── used by: app.py

reporting.py
├── depends on: pandas, plotly.express, reportlab
├── provides: visualization and PDF generation
└── used by: app.py
```

#### 4.4.2 External Dependencies

**Core Dependencies:**
- `streamlit`: Web application framework
- `pandas`: Data manipulation and analysis
- `sqlite3`: Database connectivity (built-in)
- `plotly`: Interactive visualizations
- `reportlab`: PDF generation

**Development Dependencies:**
- `pytest`: Testing framework
- `black`: Code formatting
- `flake8`: Code linting

#### 4.4.3 Dependency Management

- Dependencies are managed through requirements.txt
- Version pinning ensures reproducible deployments
- Minimal dependency footprint for easier maintenance

### 4.5 Information Viewpoint

#### 4.5.1 Data Model

**Core Entities and Relationships:**

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Customers  │    │    Sales    │    │  Products   │
│             │    │             │    │             │
│ customer_id │◄───┤customer_id  │    │ product_sku │
│ name        │    │ sale_id     │───►│ name        │
│ email       │    │ product_sku │    │ description │
│ address     │    │ quantity    │    │ unit_price  │
│ phone       │    │ unit_price  │    │ category    │
└─────────────┘    │ total_amount│    └─────────────┘
                   │ sale_date   │
                   │ tax_rate    │
                   └─────────────┘
                          │
                          ▼
                   ┌─────────────┐
                   │  Inventory  │
                   │             │
                   │ product_sku │
                   │ current_stock│
                   │ min_stock   │
                   │ max_stock   │
                   │ location    │
                   └─────────────┘
```

#### 4.5.2 Data Storage Strategy

**Database Schema:**
- SQLite database for structured data storage
- Normalized schema to reduce redundancy
- Foreign key constraints for data integrity
- Indexed columns for performance optimization

**Data Types:**
- TEXT: String data (names, descriptions, SKUs)
- INTEGER: Numeric identifiers and quantities
- REAL: Financial amounts and calculations
- DATE: Temporal data for transactions

#### 4.5.3 Data Validation Rules

- **Sales Data**: Positive quantities and amounts, valid dates
- **Customer Data**: Valid email formats, required contact information
- **Inventory Data**: Non-negative stock levels, valid SKU formats
- **Financial Data**: Proper decimal precision for monetary values

### 4.6 Patterns Viewpoint

#### 4.6.1 Architectural Patterns

**1. Layered Architecture**
- Clear separation between presentation, business, and data layers
- Each layer only communicates with adjacent layers
- Promotes maintainability and testability

**2. Model-View-Controller (MVC)**
- Adapted for Streamlit's reactive programming model
- Separation of concerns between data, presentation, and control logic

**3. Repository Pattern**
- Data access abstracted through data_manager module
- Consistent interface for database operations
- Facilitates testing and future database migrations

#### 4.6.2 Design Patterns

**1. Singleton Pattern**
- Database connection management
- Configuration settings management

**2. Factory Pattern**
- Report generation based on type
- Chart creation based on data type

**3. Observer Pattern**
- Streamlit's reactive updates
- Session state change notifications

#### 4.6.3 Data Patterns

**1. Data Transfer Object (DTO)**
- Pandas DataFrames serve as DTOs
- Structured data exchange between layers

**2. Active Record Pattern**
- Database operations encapsulated with data
- CRUD operations associated with entities

---

### 4.7 Resource Viewpoint

#### 4.7.1 Computational Resources

**Memory Requirements:**
- Base application: 50-100 MB RAM
- Data processing: Additional 10-50 MB per 10,000 records
- Peak usage during report generation: Up to 200 MB

**Storage Requirements:**
- Application files: ~10 MB
- Database growth: ~1 MB per 1,000 transactions
- Generated reports: Variable based on content

**Processing Requirements:**
- CPU: Standard business computer (2+ cores recommended)
- I/O: Local file system access for database operations
- Network: Minimal bandwidth for web interface

#### 4.7.2 Scalability Considerations

**Data Volume Limits:**
- Recommended maximum: 100,000 transactions per year
- Database size limit: 2 GB (SQLite practical limit)
- Concurrent users: 1-5 (single-instance deployment)

**Performance Optimization:**
- Database indexing on frequently queried columns
- Lazy loading of large datasets
- Caching of calculated values in session state

### 4.8 Interface Viewpoint

#### 4.8.1 User Interface Design

**Navigation Structure:**
```
Main Application
├── Dashboard
│   ├── Performance Metrics
│   ├── Monthly Summary
│   └── Tax Overview
├── Income & Expense
│   ├── Transaction Log
│   └── Add New Entry Form
├── Sales Tracker
│   ├── Sales Log
│   ├── Add Sale Form
│   └── Invoice Generation
├── Inventory Management
│   ├── Current Stock Levels
│   └── Stock Adjustments
├── Product Recipes
│   ├── BOM Management
│   └── Recipe Editor
├── Purchases
│   ├── Purchase Orders
│   └── Supplier Management
├── Customers
│   ├── Customer Directory
│   └── Customer Forms
└── Financial Goals
    ├── Goal Setting
    └── Progress Tracking
```

#### 4.8.2 Data Interface Specifications

**Input Interfaces:**
- Form-based data entry with validation
- File upload for bulk data import (CSV)
- Interactive data editors for existing records

**Output Interfaces:**
- Real-time dashboard displays
- Downloadable reports (PDF, CSV)
- Interactive charts and visualizations

#### 4.8.3 API Interface Design

**Internal API Functions:**

```python
# Data Manager Interface
def create_tables() -> None
def load_data_from_db(table_name: str) -> pd.DataFrame
def save_df_to_db(df: pd.DataFrame, table_name: str, if_exists: str) -> None
def add_income_expense(date: str, category: str, description: str,
                      amount: float, type: str) -> None

# Calculations Interface
def calculate_profit(sales_df: pd.DataFrame) -> pd.DataFrame
def calculate_profit_margin(sales_df: pd.DataFrame) -> pd.DataFrame
def calculate_tax_collected(sales_df: pd.DataFrame) -> pd.DataFrame
def update_inventory_levels(current_inventory_df: pd.DataFrame,
                           sales_df: pd.DataFrame,
                           purchase_df: pd.DataFrame) -> pd.DataFrame

# Reporting Interface
def create_monthly_summary_df(income_expense_df: pd.DataFrame,
                             sales_df: pd.DataFrame) -> pd.DataFrame
def plot_monthly_profit(summary_df: pd.DataFrame) -> plotly.graph_objects.Figure
def generate_invoice_pdf(invoice_data: dict, filename: str) -> None
```

---

## 5. Detailed Design

### 5.1 Module Specifications

#### 5.1.1 Application Module (app.py)

**Purpose:** Main application controller and user interface orchestration

**Key Functions:**
- `initialize_db()`: Database initialization and caching
- Navigation routing based on sidebar selection
- Session state management for data persistence
- Component rendering and event handling

**Design Details:**
```python
@st.cache_resource
def initialize_db():
    """Initialize database connection and create tables if needed"""
    data_manager.create_tables()
    # Load initial data if required

# Session state initialization
if 'sales_df' not in st.session_state:
    st.session_state.sales_df = data_manager.load_data_from_db('sales')

# Navigation logic
selected_tab = st.sidebar.radio("Go to", [
    "Dashboard", "Income & Expense", "Sales Tracker",
    "Inventory", "Product Recipes", "Purchases",
    "Customers", "Financial Goals"
])
```

#### 5.1.2 Data Manager Module (data_manager.py)

**Purpose:** Database operations and data access layer

**Key Components:**
- Database connection management
- Table schema definitions
- CRUD operations for all entities
- Data validation and error handling

**Database Schema:**
```sql
-- Sales table
CREATE TABLE IF NOT EXISTS sales (
    sale_id INTEGER PRIMARY KEY AUTOINCREMENT,
    sale_date TEXT NOT NULL,
    customer_id INTEGER,
    product_sku TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    discount REAL DEFAULT 0,
    tax_rate REAL DEFAULT 0,
    total_amount REAL NOT NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
);

-- Inventory table
CREATE TABLE IF NOT EXISTS inventory (
    product_sku TEXT PRIMARY KEY,
    product_name TEXT NOT NULL,
    current_stock INTEGER NOT NULL,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER DEFAULT 1000,
    unit_cost REAL NOT NULL,
    location TEXT
);

-- Additional tables for customers, products, income_expenses, etc.
```

#### 5.1.3 Calculations Module (calculations.py)

**Purpose:** Business logic and financial calculations

**Key Algorithms:**
1. **Profit Calculation:**
   ```python
   def calculate_profit(sales_df):
       sales_df['gross_profit_per_item'] = (
           sales_df['unit_price'] - sales_df['cost_of_goods_sold_per_unit']
       )
       sales_df['gross_profit'] = (
           sales_df['gross_profit_per_item'] * sales_df['quantity']
       )
       return sales_df
   ```

2. **Inventory Updates:**
   ```python
   def update_inventory_levels(current_inventory_df, sales_df, purchase_df):
       # Deduct sold quantities
       sold_quantities = sales_df.groupby('product_sku')['quantity'].sum()
       # Add purchased quantities
       purchased_quantities = purchase_df.groupby('material_sku')['quantity'].sum()
       # Update current stock levels
       return updated_inventory
   ```

#### 5.1.4 Reporting Module (reporting.py)

**Purpose:** Data visualization and report generation

**Key Features:**
- Monthly summary aggregation
- Interactive chart generation using Plotly
- PDF invoice generation using ReportLab
- Data export functionality

---

### 5.2 Data Structures

#### 5.2.1 Core Data Structures

**Sales Record Structure:**
```python
sales_record = {
    'sale_id': int,
    'sale_date': str,  # ISO format: YYYY-MM-DD
    'customer_id': int,
    'product_sku': str,
    'quantity': int,
    'unit_price': float,
    'discount': float,
    'tax_rate': float,
    'total_amount': float,
    'cost_of_goods_sold_per_unit': float,
    'gross_profit': float,
    'profit_margin_pct': float
}
```

**Inventory Record Structure:**
```python
inventory_record = {
    'product_sku': str,
    'product_name': str,
    'current_stock': int,
    'min_stock': int,
    'max_stock': int,
    'unit_cost': float,
    'location': str,
    'last_updated': str,
    'reorder_point': int
}
```

**Customer Record Structure:**
```python
customer_record = {
    'customer_id': int,
    'name': str,
    'email': str,
    'phone': str,
    'address': str,
    'city': str,
    'state': str,
    'zip_code': str,
    'created_date': str,
    'total_purchases': float
}
```

#### 5.2.2 Configuration Structures

**Application Configuration:**
```python
app_config = {
    'database_path': str,
    'default_tax_rate': float,
    'currency_symbol': str,
    'date_format': str,
    'decimal_places': int,
    'backup_frequency': str,
    'session_timeout': int
}
```

### 5.3 Algorithms

#### 5.3.1 Inventory Management Algorithm

```python
def update_inventory_levels(current_inventory_df, sales_df, purchase_df, product_log_df):
    """
    Algorithm for updating inventory levels based on sales and purchases

    Steps:
    1. Calculate total quantities sold by product
    2. Calculate total quantities purchased by material/product
    3. Process any production activities (BOM consumption)
    4. Update current stock levels
    5. Check for reorder points
    6. Generate alerts for low stock
    """

    # Step 1: Aggregate sales by product
    sold_quantities = sales_df.groupby('product_sku')['quantity'].sum().reset_index()
    sold_quantities.rename(columns={'quantity': 'sold_qty'}, inplace=True)

    # Step 2: Merge with current inventory
    updated_inventory = current_inventory_df.merge(
        sold_quantities, on='product_sku', how='left'
    )
    updated_inventory['sold_qty'] = updated_inventory['sold_qty'].fillna(0)

    # Step 3: Deduct sold quantities
    updated_inventory['current_stock'] = (
        updated_inventory['current_stock'] - updated_inventory['sold_qty']
    )

    # Step 4: Add purchased quantities
    purchased_quantities = purchase_df.groupby('material_sku')['quantity'].sum().reset_index()
    purchased_quantities.rename(columns={'quantity': 'purchased_qty'}, inplace=True)

    # Step 5: Update with purchases
    updated_inventory = updated_inventory.merge(
        purchased_quantities,
        left_on='product_sku',
        right_on='material_sku',
        how='left'
    )
    updated_inventory['purchased_qty'] = updated_inventory['purchased_qty'].fillna(0)
    updated_inventory['current_stock'] += updated_inventory['purchased_qty']

    # Step 6: Check reorder points and generate alerts
    low_stock_items = updated_inventory[
        updated_inventory['current_stock'] <= updated_inventory['min_stock']
    ]

    return updated_inventory, low_stock_items
```

#### 5.3.2 Financial Calculation Algorithm

```python
def calculate_comprehensive_financials(sales_df, expenses_df, tax_rate=0.08):
    """
    Comprehensive financial calculations including:
    - Gross profit per item and total
    - Profit margins
    - Tax calculations
    - Net profit after expenses
    """

    # Calculate gross profit per item
    sales_df['gross_profit_per_item'] = (
        sales_df['unit_price'] - sales_df['cost_of_goods_sold_per_unit']
    )

    # Calculate total gross profit
    sales_df['gross_profit'] = (
        sales_df['gross_profit_per_item'] * sales_df['quantity']
    )

    # Calculate profit margin percentage
    sales_df['profit_margin_pct'] = (
        (sales_df['gross_profit'] / sales_df['total_amount']) * 100
    ).fillna(0).round(2)

    # Calculate tax collected
    sales_df['sales_tax_collected'] = sales_df['total_amount'] * tax_rate

    # Aggregate financial summary
    total_revenue = sales_df['total_amount'].sum()
    total_gross_profit = sales_df['gross_profit'].sum()
    total_tax_collected = sales_df['sales_tax_collected'].sum()
    total_expenses = expenses_df['amount'].sum()

    net_profit = total_gross_profit - total_expenses

    financial_summary = {
        'total_revenue': total_revenue,
        'total_gross_profit': total_gross_profit,
        'total_expenses': total_expenses,
        'net_profit': net_profit,
        'profit_margin': (net_profit / total_revenue * 100) if total_revenue > 0 else 0,
        'tax_collected': total_tax_collected
    }

    return sales_df, financial_summary
```

### 5.4 Error Handling

#### 5.4.1 Database Error Handling

```python
def safe_database_operation(operation_func, *args, **kwargs):
    """
    Wrapper function for safe database operations with error handling
    """
    try:
        return operation_func(*args, **kwargs)
    except sqlite3.IntegrityError as e:
        st.error(f"Data integrity error: {str(e)}")
        return None
    except sqlite3.OperationalError as e:
        st.error(f"Database operation error: {str(e)}")
        return None
    except Exception as e:
        st.error(f"Unexpected database error: {str(e)}")
        return None
```

#### 5.4.2 Data Validation

```python
def validate_sales_data(sales_record):
    """
    Validate sales data before database insertion
    """
    errors = []

    if not sales_record.get('product_sku'):
        errors.append("Product SKU is required")

    if sales_record.get('quantity', 0) <= 0:
        errors.append("Quantity must be positive")

    if sales_record.get('unit_price', 0) <= 0:
        errors.append("Unit price must be positive")

    try:
        datetime.strptime(sales_record.get('sale_date', ''), '%Y-%m-%d')
    except ValueError:
        errors.append("Invalid date format (YYYY-MM-DD required)")

    return len(errors) == 0, errors
```

---

## 6. Design Rationale

### 6.1 Technology Choices

#### 6.1.1 Streamlit Framework
**Rationale:** Chosen for rapid development and deployment of data-driven applications
**Benefits:**
- Minimal learning curve for Python developers
- Built-in components for data visualization
- Reactive programming model suitable for business applications
- No need for separate frontend development

**Trade-offs:**
- Limited customization compared to traditional web frameworks
- Single-page application constraints
- Performance limitations with large datasets

#### 6.1.2 SQLite Database
**Rationale:** Lightweight, serverless database suitable for small to medium businesses
**Benefits:**
- Zero configuration and administration
- Cross-platform compatibility
- ACID compliance for data integrity
- Suitable for single-user or small team environments

**Trade-offs:**
- Limited concurrent write operations
- Not suitable for high-traffic applications
- Limited built-in replication features

#### 6.1.3 Pandas for Data Processing
**Rationale:** Industry-standard library for data manipulation and analysis
**Benefits:**
- Powerful data transformation capabilities
- Excellent integration with visualization libraries
- Familiar to data analysts and scientists
- Efficient handling of structured data

**Trade-offs:**
- Memory usage can be high for large datasets
- Learning curve for complex operations
- Not optimized for real-time processing

### 6.2 Architectural Decisions

#### 6.2.1 Monolithic Architecture
**Decision:** Single application deployment rather than microservices
**Rationale:**
- Simpler deployment and maintenance for small teams
- Reduced complexity in development and testing
- Appropriate scale for target user base
- Easier debugging and monitoring

#### 6.2.2 File-Based Storage
**Decision:** Local file system for database and generated reports
**Rationale:**
- Simplifies deployment requirements
- Reduces infrastructure costs
- Suitable for single-instance deployments
- Easy backup and migration

### 6.3 Design Trade-offs

#### 6.3.1 Performance vs. Simplicity
**Trade-off:** Chose simplicity over maximum performance
**Impact:** Some operations may be slower but system is easier to maintain and extend

#### 6.3.2 Flexibility vs. Ease of Use
**Trade-off:** Prioritized ease of use for non-technical users
**Impact:** Some advanced features may require code changes rather than configuration

---

## 7. Appendices

### 7.1 Database Schema Reference

#### 7.1.1 Complete Table Definitions

```sql
-- Sales table
CREATE TABLE IF NOT EXISTS sales (
    sale_id INTEGER PRIMARY KEY AUTOINCREMENT,
    sale_date TEXT NOT NULL,
    customer_id INTEGER,
    product_sku TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    discount REAL DEFAULT 0,
    tax_rate REAL DEFAULT 0,
    total_amount REAL NOT NULL,
    cost_of_goods_sold_per_unit REAL DEFAULT 0,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
);

-- Inventory table
CREATE TABLE IF NOT EXISTS inventory (
    product_sku TEXT PRIMARY KEY,
    product_name TEXT NOT NULL,
    current_stock INTEGER NOT NULL,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER DEFAULT 1000,
    unit_cost REAL NOT NULL,
    location TEXT,
    last_updated TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    created_date TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Income and Expenses table
CREATE TABLE IF NOT EXISTS income_expenses (
    transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    amount REAL NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('Income', 'Expense')),
    created_date TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    product_sku TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    unit_price REAL NOT NULL,
    unit_cost REAL NOT NULL,
    created_date TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Purchases table
CREATE TABLE IF NOT EXISTS purchases (
    purchase_id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_date TEXT NOT NULL,
    supplier_name TEXT NOT NULL,
    material_sku TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_cost REAL NOT NULL,
    total_cost REAL NOT NULL,
    created_date TEXT DEFAULT CURRENT_TIMESTAMP
);
```

### 7.2 Configuration Reference

#### 7.2.1 Environment Variables

```bash
# Database configuration
DB_PATH=./lightweight_erp.db

# Application settings
DEFAULT_TAX_RATE=0.08
CURRENCY_SYMBOL=$
DATE_FORMAT=%Y-%m-%d

# Performance settings
MAX_RECORDS_PER_PAGE=1000
CACHE_TIMEOUT=3600

# Security settings
SESSION_TIMEOUT=7200
```

### 7.3 API Reference

#### 7.3.1 Data Manager Functions

```python
# Database operations
create_tables() -> None
get_db_connection() -> sqlite3.Connection
load_data_from_db(table_name: str) -> pd.DataFrame
save_df_to_db(df: pd.DataFrame, table_name: str, if_exists: str = 'replace') -> None

# CRUD operations
add_income_expense(date: str, category: str, description: str, amount: float, type: str) -> None
add_sale(sale_data: dict) -> int
add_customer(customer_data: dict) -> int
update_inventory(product_sku: str, new_stock: int) -> bool
```

#### 7.3.2 Calculations Functions

```python
# Financial calculations
calculate_profit(sales_df: pd.DataFrame) -> pd.DataFrame
calculate_profit_margin(sales_df: pd.DataFrame) -> pd.DataFrame
calculate_tax_collected(sales_df: pd.DataFrame) -> pd.DataFrame

# Inventory calculations
update_inventory_levels(current_inventory_df: pd.DataFrame,
                       sales_df: pd.DataFrame,
                       purchase_df: pd.DataFrame) -> pd.DataFrame
calculate_reorder_points(inventory_df: pd.DataFrame,
                        sales_history_df: pd.DataFrame) -> pd.DataFrame
```

#### 7.3.3 Reporting Functions

```python
# Report generation
create_monthly_summary_df(income_expense_df: pd.DataFrame,
                         sales_df: pd.DataFrame) -> pd.DataFrame
generate_sales_report(start_date: str, end_date: str) -> pd.DataFrame
generate_inventory_report() -> pd.DataFrame

# Visualization
plot_monthly_profit(summary_df: pd.DataFrame) -> plotly.graph_objects.Figure
plot_inventory_levels(inventory_df: pd.DataFrame) -> plotly.graph_objects.Figure

# PDF generation
generate_invoice_pdf(invoice_data: dict, filename: str) -> None
generate_financial_report_pdf(financial_data: dict, filename: str) -> None
```

### 7.4 Deployment Guide

#### 7.4.1 System Requirements

**Minimum Requirements:**
- Python 3.8 or higher
- 4 GB RAM
- 1 GB available disk space
- Modern web browser

**Recommended Requirements:**
- Python 3.9 or higher
- 8 GB RAM
- 5 GB available disk space
- SSD storage for better performance

#### 7.4.2 Installation Steps

1. **Clone or download the application files**
2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
3. **Initialize the database:**
   ```bash
   python -c "import data_manager; data_manager.create_tables()"
   ```
4. **Run the application:**
   ```bash
   streamlit run app.py
   ```
5. **Access the application at:** `http://localhost:8501`

---

**Document End**

*This Software Design Document complies with IEEE Std 1016-2009 and provides comprehensive design information for the Inventory Cost Management System.*