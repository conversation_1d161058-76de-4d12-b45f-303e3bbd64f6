# data_manager.py
import pandas as pd
import sqlite3

DB_NAME = 'lightweight_erp.db'

def get_db_connection():
    conn = sqlite3.connect(DB_NAME)
    conn.row_factory = sqlite3.Row # Allows access by column name
    return conn

def create_tables():
    conn = get_db_connection()
    cursor = conn.cursor()
    # Example for Sales table - you'd have one for each category
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sales (
            sale_id INTEGER PRIMARY KEY AUTOINCREMENT,
            sale_date TEXT NOT NULL,
            customer_id INTEGER,
            product_sku TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            discount REAL DEFAULT 0,
            tax_rate REAL DEFAULT 0,
            total_amount REAL NOT NULL,
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
        )
    ''')
    # Add other CREATE TABLE statements for inventory, products, etc.
    conn.commit()
    conn.close()

def load_data_from_db(table_name):
    conn = get_db_connection()
    df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
    conn.close()
    return df

def save_df_to_db(df, table_name, if_exists='replace', index=False):
    # 'if_exists' can be 'append', 'replace', 'fail'
    conn = get_db_connection()
    df.to_sql(table_name, conn, if_exists=if_exists, index=index)
    conn.close()

def add_income_expense(date, category, description, amount, type):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("INSERT INTO income_expenses (date, category, description, amount, type) VALUES (?, ?, ?, ?, ?)",
                   (date, category, description, amount, type))
    conn.commit()
    conn.close()

# Initial setup call (run once to create tables)
# create_tables()