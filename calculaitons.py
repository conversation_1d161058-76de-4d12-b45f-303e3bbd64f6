# calculations.py
import pandas as pd

def calculate_profit(sales_df):
    # Assuming sales_df has 'quantity', 'unit_price', 'cost_of_goods_sold_per_unit'
    sales_df['gross_profit_per_item'] = sales_df['unit_price'] - sales_df['cost_of_goods_sold_per_unit']
    sales_df['gross_profit'] = sales_df['gross_profit_per_item'] * sales_df['quantity']
    return sales_df

def calculate_profit_margin(sales_df):
    # Ensure 'gross_profit' and 'total_amount' (sale price) exist
    sales_df['profit_margin_pct'] = (sales_df['gross_profit'] / sales_df['total_amount']) * 100
    sales_df['profit_margin_pct'] = sales_df['profit_margin_pct'].fillna(0).round(2) # Handle division by zero
    return sales_df

def calculate_tax_collected(sales_df):
    # Assuming 'total_amount' and 'tax_rate'
    sales_df['sales_tax_collected'] = sales_df['total_amount'] * sales_df['tax_rate']
    return sales_df
    
# calculations.py (continued)
def update_inventory_levels(current_inventory_df, sales_df, purchase_df, product_log_df):
    # Example: Deduct sold items
    sold_quantities = sales_df.groupby('product_sku')['quantity'].sum().reset_index()
    sold_quantities.rename(columns={'quantity': 'sold_qty'}, inplace=True)

    updated_inventory = current_inventory_df.merge(sold_quantities, on='product_sku', how='left')
    updated_inventory['sold_qty'] = updated_inventory['sold_qty'].fillna(0)
    updated_inventory['current_stock'] = updated_inventory['current_stock'] - updated_inventory['sold_qty']

    # Add purchased items
    purchased_quantities = purchase_df.groupby('material_sku')['quantity'].sum().reset_index()
    purchased_quantities.rename(columns={'quantity': 'purchased_qty'}, inplace=True)
    updated_inventory = updated_inventory.merge(purchased_quantities, left_on='material_sku', right_on='material_sku', how='left') # Careful with SKUs here, product vs material
    updated_inventory['purchased_qty'] = updated_inventory['purchased_qty'].fillna(0)
    # This logic needs to be carefully designed based on your SKU structure and how products relate to materials.
    # You'll likely need separate inventory DFs for raw materials and finished products.

    # Handle product creation (BOM logic)
    # This would involve looking up product recipes and deducting raw materials based on batch production.

    return updated_inventory